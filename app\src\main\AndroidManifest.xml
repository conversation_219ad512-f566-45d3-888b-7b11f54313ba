<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.INTERNET" />

    <!-- 基本蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

    <!-- 位置权限（BLE扫描需要） -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Android 12+ 蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />

    <!--相机权限-->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>


    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
    <uses-permission android:name="android.permission.SYSTEM_CAMERA"
        tools:ignore="ProtectedPermissions" />

    <!-- 允许应用修改系统设置（如屏幕亮度、Wi-Fi 状态、默认应用等）。   -->
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <!--文件读写权限    -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>

    <!-- 悬浮窗权限 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />

    <application
        android:name=".MpdApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        tools:replace="android:icon"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.AppStartLoad"
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true"
        android:extractNativeLibs="true"
        android:requestLegacyExternalStorage="true">

        <meta-data
            android:name="design_width_in_dp"
            android:value="960"/>
        <meta-data
            android:name="design_height_in_dp"
            android:value="540"/>

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".detection.DetectionActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <activity
            android:name=".detection.DetectionActivity1"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <activity
            android:name=".update.UpdateActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <activity
            android:name=".detection.hrv.HrvActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <activity
            android:name=".detection.DetectionWebActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <activity
            android:name=".config.ConfigActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <activity
            android:name=".scan.ScanActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <activity
            android:name=".MoreSettingsActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <service android:name=".gaze.track.GazeTrackService"
            android:exported="true"
            android:enabled="true"
            android:foregroundServiceType="camera"
            android:process=":gaze"/>


    </application>

</manifest>