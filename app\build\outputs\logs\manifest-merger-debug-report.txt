-- Merging decision tree log ---
manifest
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:2:1-136:12
INJECTED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:2:1-136:12
INJECTED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:2:1-136:12
INJECTED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:2:1-136:12
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:2:1-21:12
MERGED from [androidx.databinding:viewbinding:8.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\107ef3c689762754d687a6eca283bfe6\transformed\jetified-viewbinding-8.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:17:1-48:12
MERGED from [io.getstream:stream-log-android:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\01b40dbe62c6fbba0eb13e7ba0fdf334\transformed\jetified-stream-log-android-1.1.4\AndroidManifest.xml:17:1-24:12
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:2:1-22:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\09e94a311f42d674eb715371ac8d596c\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\50ccd012f8006cd9a6316ace317828ed\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a7dc3ec56c635a7873ef2281b1cede4\transformed\jetified-lottie-6.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.camera:camera-video:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4ea2378e1282e0fb536810f07b7bf93b\transformed\jetified-camera-video-1.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\728f2b84f64e685ae69899a1bdc516b0\transformed\jetified-camera-lifecycle-1.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\5dcf23016f5fd81271748c4a43ea45ef\transformed\jetified-camera-core-1.4.2\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\d2b2867e1f7a04ce611c1533259dee9d\transformed\jetified-camera-view-1.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\06bc9697558447f752b97a507c06bb1b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc2893f89eae3ba97224142d0dcaa4a8\transformed\jetified-barcode-scanning-17.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:face-detection:16.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bf6c3cb5840c02cc9f768a2f9f39e79c\transformed\jetified-face-detection-16.1.7\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\98cb6cffa8655ab6507ce428bc5ad66e\transformed\jetified-barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ede9995337ea73b4d0233d500609b091\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\97b2440c29059681c04a5336dc04ae7e\transformed\jetified-fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1\AndroidManifest.xml:2:1-15:12
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9254240051e84bb84ac5061829e25df2\transformed\jetified-glide-transformations-4.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bd3eae0f86996815ebc23cc6048bb94\transformed\jetified-glide-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0041beb24dec71b8781b322d4e2ed1b8\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.github.jeremyliao:live-event-bus-x:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\72c76e291e940458346c0f7672d72a79\transformed\jetified-live-event-bus-x-1.8.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\37c0572a772a3210daa7784c0db88cd4\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4b928c7bad8580dee32a0714ef5a363\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e558dea2d0a70667137efa91d48cf54c\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\448265791efcfaa1041f3e7728cd0633\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\73c31c96b3aaf5c40e722e923ba58c4e\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\42c76c7d0438bd8e341c573f2b121faa\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6e8f5581e8c0d1be90b1111773aeb1ef\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fb7343f306845a798c394ec294e9cc5b\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe94a204f7553dc38c178bdc8bcdfa60\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\40c7a5108dba4e3b8da3f0a7ddc771f9\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\96ee904e48e7583859b6a0d2569ec642\transformed\jetified-lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\a38bec121c9b55f1b7513221109f9a56\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\90e6daa8b8789c4f9a5c06e0fc803fe1\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0845ad2cb2b2447decc3bb48754d66bf\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8923ccca9bc9c475bff05ec6a0afba2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\37c9fa3b7c8e77334333447bae5f1b85\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ea1c800477ce1b3a767b6ea904c5be97\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5f3a99595410223d342783841888f712\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\efd467293e0b9b5a51777a2be79e83eb\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-extractor:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\fc8f3dcb384bdb4af8140e654e58b5f6\transformed\jetified-media3-extractor-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe8e4784db1986d494d8c00c089bb056\transformed\jetified-media3-container-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\88dbb572ec5c7cefe6d224189160b960\transformed\jetified-media3-datasource-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\83aa90154ad1dd42a85ec1dfee1708ab\transformed\jetified-media3-decoder-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f44bfcd6e0d1777129c96309cc8d8cd7\transformed\jetified-media3-database-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a53940ce7983efb289ed3d9246f4a3b\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\37eb7cb3503359f8e3891a2adf804078\transformed\jetified-media3-ui-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\a614db70bfd78a9cd3f1844282edd760\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4273d528f5a5d74e4a86eb85c2428961\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed7ca423da9b209cd5734a0b83a7b176\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e01ee8b33fbdfb5ae0ab71b40012f92\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\48c0bc05c01b91db76e154bf0589d5bb\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ee250af5b9082ea1e2e85e68e589d22\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1ccf6ad3c1a3ed7a07102cfe1748e55\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c192c6c8af2a0e318e157ddf9673a0b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\afc9fcec63ee624454c543e8d82e4d76\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2a22b45a408eabbd10f0ec3a4ed4ba7\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\65d57ebc8021fcfcca12f6c2e8e7b242\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\df8e87f1265f72089fb8491f41a438a4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\cef52979e41f21c52c2ea8c565eba0fd\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\9f43da10b30857b33feca34b3cd03c34\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\60bf26a59901bc3bb9011380fbd89190\transformed\jetified-lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e09d87722e8e31fc053c3f58a118f51a\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f165c8addaa916cc537873b884351d0\transformed\jetified-room-ktx-2.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:17:1-31:12
MERGED from [com.zhy:okhttputils:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d045de4695537f487ca01dc3ca7c12ca\transformed\jetified-okhttputils-2.6.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.aliyun.alink.linksdk:lp-iot-linkkit:1.7.3.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d78d3da81bb6f8253ff6eae65f505b9\transformed\jetified-lp-iot-linkkit-1.7.3.8\AndroidManifest.xml:2:1-15:12
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:2:1-21:12
MERGED from [no.nordicsemi.android:ble:2.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\b28b1524e2778f44671542e19c926bed\transformed\jetified-ble-2.2.4\AndroidManifest.xml:2:1-13:12
MERGED from [com.tencent:mmkv-static:1.2.16] C:\Users\<USER>\.gradle\caches\transforms-3\e0558eea8bbe483897f828c22b0e5ef0\transformed\jetified-mmkv-static-1.2.16\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\84b527003f3a0e5a3267cc12425ea2d6\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\772c204bca40e9f044202cb0c35a6a24\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\be756bf3eb4e1c85b3fe0da5bfd9d804\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.orhanobut:logger:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a4aaf90b674c44aae35bd13a5aeec7b\transformed\jetified-logger-2.2.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\8528862a611804d35d26316bb37d3b92\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c75f16fb3532e702d63c57107fce1c7\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\83cce0b53247cf5607abf9a7fd5167ef\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\377b6447c16dbc5d000cfe1af300650d\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4b5777ed0d4bffa3f904e5b76ee57f60\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\de074a4a358f597debea3416bcaa465b\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a33641c1b8e8c40de42d22eab0154eb5\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b81fee8ac482d588760b3e70ccd1be41\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7be3f3db6c752ace90a9d984f67f1bed\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\709da5e1d2fd883e92f87ad1d3d223db\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7dcaaec5891935906304315c5e8854e0\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e4ce545fcd52fce8e813db51c5cadbd\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e33cfc4915e44ae2c9dba5d1887dc68\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d822517d86f4768802c1e5413aee44b7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9cce644cafebd40ffd9b00f6518ccfc\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f15a1f0b657363ac1155c0f1898a532\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\0168da5f54e3519cfdce27f2e9820640\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:2:1-19:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\52482d92e32156ad3b52bb767e08bae5\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [io.github.jeremyliao:lebx-processor-gson:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\9aed58bccf8b32935a6e7dd7029d817a\transformed\jetified-lebx-processor-gson-1.8.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:2:1-21:12
MERGED from [com.aliyun.alink.linksdk:lp-public-cmp:1.9.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\9805520c9e9935306579be110e607219\transformed\jetified-lp-public-cmp-1.9.3.5\AndroidManifest.xml:2:1-18:12
MERGED from [com.aliyun.alink.linksdk:lp-connectsdk:1.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\12b3884315fe7041a0ff571808979902\transformed\jetified-lp-connectsdk-1.0.6\AndroidManifest.xml:2:1-13:12
MERGED from [com.aliyun.alink.linksdk:iot-h2-stream:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\ffb78f1d2813c7eaae96662549550197\transformed\jetified-iot-h2-stream-1.1.6\AndroidManifest.xml:2:1-18:12
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:2:1-22:12
MERGED from [com.aliyun.alink.linksdk:public-channel-gateway:1.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\89a67f32f2b6dbb5229bb80cd6225108\transformed\jetified-public-channel-gateway-1.6.4\AndroidManifest.xml:2:1-18:12
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:2:1-23:12
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:2:1-29:12
MERGED from [com.aliyun.alink.linksdk:tools:1.3.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c9ad81920ad96590b6c7c72106da5b5\transformed\jetified-tools-1.3.5.1\AndroidManifest.xml:2:1-16:12
MERGED from [com.aliyun.alink.linksdk:iot-h2:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\9ae93d416fb6b56551d4040db1b3ec30\transformed\jetified-iot-h2-1.1.6\AndroidManifest.xml:2:1-19:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\a6a3a9b2b75d99a97087666cb907f0ff\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
MERGED from [com.aliyun.alink.linksdk:api-client-biz:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b5c364f047faafa411b81befedb2ecf\transformed\jetified-api-client-biz-1.0.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.aliyun.alink.linksdk:android_alcs_lpbs:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e43030a83b65b1cf7711375223358c6\transformed\jetified-android_alcs_lpbs-1.7.0\AndroidManifest.xml:2:1-19:12
MERGED from [com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\31345907932cb16ab07cdd18e821ff6d\transformed\jetified-public-alcs-cmp-1.6.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.aliyun.alink.linksdk:opensource-paho:1.2.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\f3424d509a08750eee3f2c151cac5884\transformed\jetified-opensource-paho-1.2.0.6\AndroidManifest.xml:2:1-12:12
	package
		INJECTED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml
	android:sharedUserId
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:4:5-46
	android:versionName
		INJECTED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:11:5-67
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:11:5-67
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:26:5-67
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:26:5-67
MERGED from [com.aliyun.alink.linksdk:lp-iot-linkkit:1.7.3.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d78d3da81bb6f8253ff6eae65f505b9\transformed\jetified-lp-iot-linkkit-1.7.3.8\AndroidManifest.xml:11:5-67
MERGED from [com.aliyun.alink.linksdk:lp-iot-linkkit:1.7.3.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d78d3da81bb6f8253ff6eae65f505b9\transformed\jetified-lp-iot-linkkit-1.7.3.8\AndroidManifest.xml:11:5-67
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:14:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
MERGED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:17:5-67
MERGED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:17:5-67
MERGED from [com.aliyun.alink.linksdk:lp-public-cmp:1.9.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\9805520c9e9935306579be110e607219\transformed\jetified-lp-public-cmp-1.9.3.5\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:lp-public-cmp:1.9.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\9805520c9e9935306579be110e607219\transformed\jetified-lp-public-cmp-1.9.3.5\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:iot-h2-stream:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\ffb78f1d2813c7eaae96662549550197\transformed\jetified-iot-h2-stream-1.1.6\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:iot-h2-stream:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\ffb78f1d2813c7eaae96662549550197\transformed\jetified-iot-h2-stream-1.1.6\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:11:5-67
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:11:5-67
MERGED from [com.aliyun.alink.linksdk:public-channel-gateway:1.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\89a67f32f2b6dbb5229bb80cd6225108\transformed\jetified-public-channel-gateway-1.6.4\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:public-channel-gateway:1.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\89a67f32f2b6dbb5229bb80cd6225108\transformed\jetified-public-channel-gateway-1.6.4\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:16:5-67
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:16:5-67
MERGED from [com.aliyun.alink.linksdk:iot-h2:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\9ae93d416fb6b56551d4040db1b3ec30\transformed\jetified-iot-h2-1.1.6\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:iot-h2:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\9ae93d416fb6b56551d4040db1b3ec30\transformed\jetified-iot-h2-1.1.6\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:api-client-biz:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b5c364f047faafa411b81befedb2ecf\transformed\jetified-api-client-biz-1.0.0\AndroidManifest.xml:11:5-67
MERGED from [com.aliyun.alink.linksdk:api-client-biz:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b5c364f047faafa411b81befedb2ecf\transformed\jetified-api-client-biz-1.0.0\AndroidManifest.xml:11:5-67
MERGED from [com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\31345907932cb16ab07cdd18e821ff6d\transformed\jetified-public-alcs-cmp-1.6.0\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\31345907932cb16ab07cdd18e821ff6d\transformed\jetified-public-alcs-cmp-1.6.0\AndroidManifest.xml:14:5-67
MERGED from [com.aliyun.alink.linksdk:opensource-paho:1.2.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\f3424d509a08750eee3f2c151cac5884\transformed\jetified-opensource-paho-1.2.0.6\AndroidManifest.xml:8:5-67
MERGED from [com.aliyun.alink.linksdk:opensource-paho:1.2.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\f3424d509a08750eee3f2c151cac5884\transformed\jetified-opensource-paho-1.2.0.6\AndroidManifest.xml:8:5-67
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.BLUETOOTH
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:9:5-68
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:10:5-68
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:10:5-68
MERGED from [no.nordicsemi.android:ble:2.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\b28b1524e2778f44671542e19c926bed\transformed\jetified-ble-2.2.4\AndroidManifest.xml:10:5-68
MERGED from [no.nordicsemi.android:ble:2.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\b28b1524e2778f44671542e19c926bed\transformed\jetified-ble-2.2.4\AndroidManifest.xml:10:5-68
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:9:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:10:5-74
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:11:5-74
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:11:5-74
MERGED from [no.nordicsemi.android:ble:2.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\b28b1524e2778f44671542e19c926bed\transformed\jetified-ble-2.2.4\AndroidManifest.xml:11:5-74
MERGED from [no.nordicsemi.android:ble:2.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\b28b1524e2778f44671542e19c926bed\transformed\jetified-ble-2.2.4\AndroidManifest.xml:11:5-74
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:10:22-71
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:13:5-79
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:16:5-79
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:16:5-79
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:8:5-79
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:8:5-79
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:12:5-79
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:12:5-79
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:13:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:14:5-81
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:15:5-81
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:15:5-81
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:9:5-81
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:9:5-81
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:14:22-78
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:17:5-73
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:13:5-73
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:13:5-73
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:17:22-70
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:18:5-76
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:12:5-76
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:12:5-76
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:14:5-76
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:14:5-76
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:18:22-73
uses-permission#android.permission.BLUETOOTH_ADVERTISE
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:19:5-78
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:14:5-78
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:14:5-78
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:19:22-75
uses-permission#android.permission.CAMERA
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:22:5-65
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:22:22-62
uses-feature#android.hardware.camera
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:23:5-60
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:23:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:24:5-70
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:24:19-67
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:26:5-80
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:28:5-81
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:28:5-81
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:17:5-81
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:17:5-81
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:11:5-81
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:11:5-81
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:26:22-78
uses-permission#android.permission.FOREGROUND_SERVICE_CAMERA
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:29:5-84
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:29:22-81
uses-permission#android.permission.SYSTEM_CAMERA
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:30:5-31:47
	tools:ignore
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:31:9-44
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:30:22-69
uses-permission#android.permission.WRITE_SETTINGS
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:34:5-73
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:15:5-73
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:15:5-73
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:34:22-70
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:37:5-80
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:27:5-80
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:27:5-80
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:18:5-80
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:18:5-80
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:17:5-80
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:17:5-80
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:37:22-77
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:39:5-71
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:39:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:41:5-80
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:41:22-77
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:43:5-82
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:43:22-80
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:46:5-78
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:46:22-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:49:5-77
MERGED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:22:5-77
MERGED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:22:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:49:22-74
application
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:52:5-134:19
MERGED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:24:5-46:19
MERGED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:24:5-46:19
MERGED from [io.getstream:stream-log-android:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\01b40dbe62c6fbba0eb13e7ba0fdf334\transformed\jetified-stream-log-android-1.1.4\AndroidManifest.xml:22:5-20
MERGED from [io.getstream:stream-log-android:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\01b40dbe62c6fbba0eb13e7ba0fdf334\transformed\jetified-stream-log-android-1.1.4\AndroidManifest.xml:22:5-20
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:19:5-20:19
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:19:5-20:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\09e94a311f42d674eb715371ac8d596c\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\09e94a311f42d674eb715371ac8d596c\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\50ccd012f8006cd9a6316ace317828ed\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\50ccd012f8006cd9a6316ace317828ed\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a7dc3ec56c635a7873ef2281b1cede4\transformed\jetified-lottie-6.0.0\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a7dc3ec56c635a7873ef2281b1cede4\transformed\jetified-lottie-6.0.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\5dcf23016f5fd81271748c4a43ea45ef\transformed\jetified-camera-core-1.4.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\5dcf23016f5fd81271748c4a43ea45ef\transformed\jetified-camera-core-1.4.2\AndroidManifest.xml:23:5-34:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1\AndroidManifest.xml:9:5-13:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1\AndroidManifest.xml:9:5-13:19
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bd3eae0f86996815ebc23cc6048bb94\transformed\jetified-glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bd3eae0f86996815ebc23cc6048bb94\transformed\jetified-glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\37c0572a772a3210daa7784c0db88cd4\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\37c0572a772a3210daa7784c0db88cd4\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e558dea2d0a70667137efa91d48cf54c\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e558dea2d0a70667137efa91d48cf54c\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [com.aliyun.alink.linksdk:lp-iot-linkkit:1.7.3.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d78d3da81bb6f8253ff6eae65f505b9\transformed\jetified-lp-iot-linkkit-1.7.3.8\AndroidManifest.xml:13:5-20
MERGED from [com.aliyun.alink.linksdk:lp-iot-linkkit:1.7.3.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d78d3da81bb6f8253ff6eae65f505b9\transformed\jetified-lp-iot-linkkit-1.7.3.8\AndroidManifest.xml:13:5-20
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:17:5-19:47
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:17:5-19:47
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\8528862a611804d35d26316bb37d3b92\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\8528862a611804d35d26316bb37d3b92\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\83cce0b53247cf5607abf9a7fd5167ef\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\83cce0b53247cf5607abf9a7fd5167ef\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4b5777ed0d4bffa3f904e5b76ee57f60\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4b5777ed0d4bffa3f904e5b76ee57f60\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:11:5-17:19
MERGED from [me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:11:5-17:19
MERGED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:19:5-53
MERGED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:19:5-53
MERGED from [com.aliyun.alink.linksdk:lp-public-cmp:1.9.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\9805520c9e9935306579be110e607219\transformed\jetified-lp-public-cmp-1.9.3.5\AndroidManifest.xml:16:5-20
MERGED from [com.aliyun.alink.linksdk:lp-public-cmp:1.9.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\9805520c9e9935306579be110e607219\transformed\jetified-lp-public-cmp-1.9.3.5\AndroidManifest.xml:16:5-20
MERGED from [com.aliyun.alink.linksdk:iot-h2-stream:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\ffb78f1d2813c7eaae96662549550197\transformed\jetified-iot-h2-stream-1.1.6\AndroidManifest.xml:16:5-20
MERGED from [com.aliyun.alink.linksdk:iot-h2-stream:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\ffb78f1d2813c7eaae96662549550197\transformed\jetified-iot-h2-stream-1.1.6\AndroidManifest.xml:16:5-20
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:16:5-20:19
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:16:5-20:19
MERGED from [com.aliyun.alink.linksdk:public-channel-gateway:1.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\89a67f32f2b6dbb5229bb80cd6225108\transformed\jetified-public-channel-gateway-1.6.4\AndroidManifest.xml:16:5-20
MERGED from [com.aliyun.alink.linksdk:public-channel-gateway:1.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\89a67f32f2b6dbb5229bb80cd6225108\transformed\jetified-public-channel-gateway-1.6.4\AndroidManifest.xml:16:5-20
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:20:5-21:19
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:20:5-21:19
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:23:5-27:38
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:23:5-27:38
MERGED from [com.aliyun.alink.linksdk:tools:1.3.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c9ad81920ad96590b6c7c72106da5b5\transformed\jetified-tools-1.3.5.1\AndroidManifest.xml:14:5-20
MERGED from [com.aliyun.alink.linksdk:tools:1.3.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c9ad81920ad96590b6c7c72106da5b5\transformed\jetified-tools-1.3.5.1\AndroidManifest.xml:14:5-20
MERGED from [com.aliyun.alink.linksdk:iot-h2:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\9ae93d416fb6b56551d4040db1b3ec30\transformed\jetified-iot-h2-1.1.6\AndroidManifest.xml:17:5-20
MERGED from [com.aliyun.alink.linksdk:iot-h2:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\9ae93d416fb6b56551d4040db1b3ec30\transformed\jetified-iot-h2-1.1.6\AndroidManifest.xml:17:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\a6a3a9b2b75d99a97087666cb907f0ff\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\a6a3a9b2b75d99a97087666cb907f0ff\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.aliyun.alink.linksdk:api-client-biz:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b5c364f047faafa411b81befedb2ecf\transformed\jetified-api-client-biz-1.0.0\AndroidManifest.xml:14:5-15:19
MERGED from [com.aliyun.alink.linksdk:api-client-biz:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b5c364f047faafa411b81befedb2ecf\transformed\jetified-api-client-biz-1.0.0\AndroidManifest.xml:14:5-15:19
MERGED from [com.aliyun.alink.linksdk:android_alcs_lpbs:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e43030a83b65b1cf7711375223358c6\transformed\jetified-android_alcs_lpbs-1.7.0\AndroidManifest.xml:14:5-17:19
MERGED from [com.aliyun.alink.linksdk:android_alcs_lpbs:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e43030a83b65b1cf7711375223358c6\transformed\jetified-android_alcs_lpbs-1.7.0\AndroidManifest.xml:14:5-17:19
MERGED from [com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\31345907932cb16ab07cdd18e821ff6d\transformed\jetified-public-alcs-cmp-1.6.0\AndroidManifest.xml:16:5-20
MERGED from [com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\31345907932cb16ab07cdd18e821ff6d\transformed\jetified-public-alcs-cmp-1.6.0\AndroidManifest.xml:16:5-20
MERGED from [com.aliyun.alink.linksdk:opensource-paho:1.2.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\f3424d509a08750eee3f2c151cac5884\transformed\jetified-opensource-paho-1.2.0.6\AndroidManifest.xml:10:5-20
MERGED from [com.aliyun.alink.linksdk:opensource-paho:1.2.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\f3424d509a08750eee3f2c151cac5884\transformed\jetified-opensource-paho-1.2.0.6\AndroidManifest.xml:10:5-20
	android:requestLegacyExternalStorage
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:66:9-52
	android:extractNativeLibs
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:65:9-41
	android:roundIcon
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:60:9-48
	android:icon
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:57:9-43
		REJECTED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:25:9-43
	android:networkSecurityConfig
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:63:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:61:9-35
	android:label
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:59:9-41
	android:fullBackupContent
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:56:9-54
	android:allowBackup
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:54:9-35
		REJECTED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:17:9-36
		REJECTED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:24:9-35
	android:theme
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:62:9-50
	android:dataExtractionRules
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:55:9-65
	tools:replace
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:58:9-37
	android:usesCleartextTraffic
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:64:9-44
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:53:9-39
meta-data#design_width_in_dp
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:68:9-70:34
	android:value
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:70:13-32
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:69:13-46
meta-data#design_height_in_dp
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:71:9-73:34
	android:value
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:73:13-32
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:72:13-47
activity#com.airdoc.mpd.MainActivity
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:75:9-85:20
	android:launchMode
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:78:13-43
	android:exported
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:77:13-36
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:76:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:80:13-84:29
action#android.intent.action.MAIN
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:81:17-69
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:81:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:83:17-77
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:83:27-74
activity#com.airdoc.mpd.detection.DetectionActivity
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:87:9-90:20
	android:configChanges
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:89:13-94
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:88:13-56
activity#com.airdoc.mpd.detection.DetectionActivity1
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:92:9-95:20
	android:configChanges
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:94:13-94
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:93:13-57
activity#com.airdoc.mpd.update.UpdateActivity
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:97:9-100:20
	android:configChanges
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:99:13-94
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:98:13-50
activity#com.airdoc.mpd.detection.hrv.HrvActivity
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:102:9-105:20
	android:configChanges
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:104:13-94
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:103:13-54
activity#com.airdoc.mpd.detection.DetectionWebActivity
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:107:9-110:20
	android:configChanges
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:109:13-94
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:108:13-59
activity#com.airdoc.mpd.config.ConfigActivity
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:112:9-115:20
	android:configChanges
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:114:13-94
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:113:13-50
activity#com.airdoc.mpd.scan.ScanActivity
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:117:9-120:20
	android:configChanges
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:119:13-94
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:118:13-46
activity#com.airdoc.mpd.MoreSettingsActivity
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:122:9-125:20
	android:configChanges
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:124:13-94
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:123:13-49
service#com.airdoc.mpd.gaze.track.GazeTrackService
ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:127:9-131:38
	android:process
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:131:13-36
	android:enabled
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:129:13-35
	android:exported
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:128:13-36
	android:foregroundServiceType
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:130:13-51
	android:name
		ADDED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml:127:18-61
uses-sdk
INJECTED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml
INJECTED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:6:5-8:41
MERGED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.databinding:viewbinding:8.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\107ef3c689762754d687a6eca283bfe6\transformed\jetified-viewbinding-8.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\107ef3c689762754d687a6eca283bfe6\transformed\jetified-viewbinding-8.3.1\AndroidManifest.xml:5:5-44
MERGED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:20:5-44
MERGED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:20:5-44
MERGED from [io.getstream:stream-log-android:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\01b40dbe62c6fbba0eb13e7ba0fdf334\transformed\jetified-stream-log-android-1.1.4\AndroidManifest.xml:20:5-44
MERGED from [io.getstream:stream-log-android:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\01b40dbe62c6fbba0eb13e7ba0fdf334\transformed\jetified-stream-log-android-1.1.4\AndroidManifest.xml:20:5-44
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:6:5-44
MERGED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\09e94a311f42d674eb715371ac8d596c\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\09e94a311f42d674eb715371ac8d596c\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\50ccd012f8006cd9a6316ace317828ed\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\50ccd012f8006cd9a6316ace317828ed\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a7dc3ec56c635a7873ef2281b1cede4\transformed\jetified-lottie-6.0.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a7dc3ec56c635a7873ef2281b1cede4\transformed\jetified-lottie-6.0.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.camera:camera-video:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4ea2378e1282e0fb536810f07b7bf93b\transformed\jetified-camera-video-1.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4ea2378e1282e0fb536810f07b7bf93b\transformed\jetified-camera-video-1.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\728f2b84f64e685ae69899a1bdc516b0\transformed\jetified-camera-lifecycle-1.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\728f2b84f64e685ae69899a1bdc516b0\transformed\jetified-camera-lifecycle-1.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\5dcf23016f5fd81271748c4a43ea45ef\transformed\jetified-camera-core-1.4.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\5dcf23016f5fd81271748c4a43ea45ef\transformed\jetified-camera-core-1.4.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\d2b2867e1f7a04ce611c1533259dee9d\transformed\jetified-camera-view-1.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\d2b2867e1f7a04ce611c1533259dee9d\transformed\jetified-camera-view-1.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\06bc9697558447f752b97a507c06bb1b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\06bc9697558447f752b97a507c06bb1b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc2893f89eae3ba97224142d0dcaa4a8\transformed\jetified-barcode-scanning-17.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc2893f89eae3ba97224142d0dcaa4a8\transformed\jetified-barcode-scanning-17.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:face-detection:16.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bf6c3cb5840c02cc9f768a2f9f39e79c\transformed\jetified-face-detection-16.1.7\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:face-detection:16.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bf6c3cb5840c02cc9f768a2f9f39e79c\transformed\jetified-face-detection-16.1.7\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\98cb6cffa8655ab6507ce428bc5ad66e\transformed\jetified-barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\98cb6cffa8655ab6507ce428bc5ad66e\transformed\jetified-barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ede9995337ea73b4d0233d500609b091\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ede9995337ea73b4d0233d500609b091\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\97b2440c29059681c04a5336dc04ae7e\transformed\jetified-fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\97b2440c29059681c04a5336dc04ae7e\transformed\jetified-fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9254240051e84bb84ac5061829e25df2\transformed\jetified-glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9254240051e84bb84ac5061829e25df2\transformed\jetified-glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bd3eae0f86996815ebc23cc6048bb94\transformed\jetified-glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bd3eae0f86996815ebc23cc6048bb94\transformed\jetified-glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0041beb24dec71b8781b322d4e2ed1b8\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0041beb24dec71b8781b322d4e2ed1b8\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.github.jeremyliao:live-event-bus-x:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\72c76e291e940458346c0f7672d72a79\transformed\jetified-live-event-bus-x-1.8.0\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.jeremyliao:live-event-bus-x:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\72c76e291e940458346c0f7672d72a79\transformed\jetified-live-event-bus-x-1.8.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\37c0572a772a3210daa7784c0db88cd4\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\37c0572a772a3210daa7784c0db88cd4\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4b928c7bad8580dee32a0714ef5a363\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4b928c7bad8580dee32a0714ef5a363\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e558dea2d0a70667137efa91d48cf54c\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e558dea2d0a70667137efa91d48cf54c\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\448265791efcfaa1041f3e7728cd0633\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\448265791efcfaa1041f3e7728cd0633\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\73c31c96b3aaf5c40e722e923ba58c4e\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\73c31c96b3aaf5c40e722e923ba58c4e\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\42c76c7d0438bd8e341c573f2b121faa\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\42c76c7d0438bd8e341c573f2b121faa\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6e8f5581e8c0d1be90b1111773aeb1ef\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6e8f5581e8c0d1be90b1111773aeb1ef\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fb7343f306845a798c394ec294e9cc5b\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fb7343f306845a798c394ec294e9cc5b\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe94a204f7553dc38c178bdc8bcdfa60\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe94a204f7553dc38c178bdc8bcdfa60\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\40c7a5108dba4e3b8da3f0a7ddc771f9\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\40c7a5108dba4e3b8da3f0a7ddc771f9\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\96ee904e48e7583859b6a0d2569ec642\transformed\jetified-lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\96ee904e48e7583859b6a0d2569ec642\transformed\jetified-lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\a38bec121c9b55f1b7513221109f9a56\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\a38bec121c9b55f1b7513221109f9a56\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\90e6daa8b8789c4f9a5c06e0fc803fe1\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\90e6daa8b8789c4f9a5c06e0fc803fe1\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0845ad2cb2b2447decc3bb48754d66bf\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0845ad2cb2b2447decc3bb48754d66bf\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8923ccca9bc9c475bff05ec6a0afba2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8923ccca9bc9c475bff05ec6a0afba2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\37c9fa3b7c8e77334333447bae5f1b85\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\37c9fa3b7c8e77334333447bae5f1b85\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ea1c800477ce1b3a767b6ea904c5be97\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ea1c800477ce1b3a767b6ea904c5be97\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5f3a99595410223d342783841888f712\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5f3a99595410223d342783841888f712\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\efd467293e0b9b5a51777a2be79e83eb\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\efd467293e0b9b5a51777a2be79e83eb\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\fc8f3dcb384bdb4af8140e654e58b5f6\transformed\jetified-media3-extractor-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\fc8f3dcb384bdb4af8140e654e58b5f6\transformed\jetified-media3-extractor-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe8e4784db1986d494d8c00c089bb056\transformed\jetified-media3-container-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe8e4784db1986d494d8c00c089bb056\transformed\jetified-media3-container-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\88dbb572ec5c7cefe6d224189160b960\transformed\jetified-media3-datasource-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\88dbb572ec5c7cefe6d224189160b960\transformed\jetified-media3-datasource-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\83aa90154ad1dd42a85ec1dfee1708ab\transformed\jetified-media3-decoder-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\83aa90154ad1dd42a85ec1dfee1708ab\transformed\jetified-media3-decoder-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f44bfcd6e0d1777129c96309cc8d8cd7\transformed\jetified-media3-database-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f44bfcd6e0d1777129c96309cc8d8cd7\transformed\jetified-media3-database-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a53940ce7983efb289ed3d9246f4a3b\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a53940ce7983efb289ed3d9246f4a3b\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\37eb7cb3503359f8e3891a2adf804078\transformed\jetified-media3-ui-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\37eb7cb3503359f8e3891a2adf804078\transformed\jetified-media3-ui-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\a614db70bfd78a9cd3f1844282edd760\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\a614db70bfd78a9cd3f1844282edd760\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4273d528f5a5d74e4a86eb85c2428961\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4273d528f5a5d74e4a86eb85c2428961\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed7ca423da9b209cd5734a0b83a7b176\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed7ca423da9b209cd5734a0b83a7b176\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e01ee8b33fbdfb5ae0ab71b40012f92\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e01ee8b33fbdfb5ae0ab71b40012f92\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\48c0bc05c01b91db76e154bf0589d5bb\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\48c0bc05c01b91db76e154bf0589d5bb\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ee250af5b9082ea1e2e85e68e589d22\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ee250af5b9082ea1e2e85e68e589d22\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1ccf6ad3c1a3ed7a07102cfe1748e55\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1ccf6ad3c1a3ed7a07102cfe1748e55\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c192c6c8af2a0e318e157ddf9673a0b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c192c6c8af2a0e318e157ddf9673a0b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\afc9fcec63ee624454c543e8d82e4d76\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\afc9fcec63ee624454c543e8d82e4d76\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2a22b45a408eabbd10f0ec3a4ed4ba7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2a22b45a408eabbd10f0ec3a4ed4ba7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\65d57ebc8021fcfcca12f6c2e8e7b242\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\65d57ebc8021fcfcca12f6c2e8e7b242\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\df8e87f1265f72089fb8491f41a438a4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\df8e87f1265f72089fb8491f41a438a4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\cef52979e41f21c52c2ea8c565eba0fd\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\cef52979e41f21c52c2ea8c565eba0fd\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\9f43da10b30857b33feca34b3cd03c34\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\9f43da10b30857b33feca34b3cd03c34\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\60bf26a59901bc3bb9011380fbd89190\transformed\jetified-lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\60bf26a59901bc3bb9011380fbd89190\transformed\jetified-lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e09d87722e8e31fc053c3f58a118f51a\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e09d87722e8e31fc053c3f58a118f51a\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f165c8addaa916cc537873b884351d0\transformed\jetified-room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f165c8addaa916cc537873b884351d0\transformed\jetified-room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:22:5-24:41
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:22:5-24:41
MERGED from [com.zhy:okhttputils:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d045de4695537f487ca01dc3ca7c12ca\transformed\jetified-okhttputils-2.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.zhy:okhttputils:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d045de4695537f487ca01dc3ca7c12ca\transformed\jetified-okhttputils-2.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.alink.linksdk:lp-iot-linkkit:1.7.3.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d78d3da81bb6f8253ff6eae65f505b9\transformed\jetified-lp-iot-linkkit-1.7.3.8\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.alink.linksdk:lp-iot-linkkit:1.7.3.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d78d3da81bb6f8253ff6eae65f505b9\transformed\jetified-lp-iot-linkkit-1.7.3.8\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:8:5-10:41
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:8:5-10:41
MERGED from [no.nordicsemi.android:ble:2.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\b28b1524e2778f44671542e19c926bed\transformed\jetified-ble-2.2.4\AndroidManifest.xml:6:5-8:41
MERGED from [no.nordicsemi.android:ble:2.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\b28b1524e2778f44671542e19c926bed\transformed\jetified-ble-2.2.4\AndroidManifest.xml:6:5-8:41
MERGED from [com.tencent:mmkv-static:1.2.16] C:\Users\<USER>\.gradle\caches\transforms-3\e0558eea8bbe483897f828c22b0e5ef0\transformed\jetified-mmkv-static-1.2.16\AndroidManifest.xml:5:5-7:41
MERGED from [com.tencent:mmkv-static:1.2.16] C:\Users\<USER>\.gradle\caches\transforms-3\e0558eea8bbe483897f828c22b0e5ef0\transformed\jetified-mmkv-static-1.2.16\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\84b527003f3a0e5a3267cc12425ea2d6\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\84b527003f3a0e5a3267cc12425ea2d6\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\772c204bca40e9f044202cb0c35a6a24\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\772c204bca40e9f044202cb0c35a6a24\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\be756bf3eb4e1c85b3fe0da5bfd9d804\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\be756bf3eb4e1c85b3fe0da5bfd9d804\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.orhanobut:logger:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a4aaf90b674c44aae35bd13a5aeec7b\transformed\jetified-logger-2.2.0\AndroidManifest.xml:6:5-43
MERGED from [com.orhanobut:logger:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a4aaf90b674c44aae35bd13a5aeec7b\transformed\jetified-logger-2.2.0\AndroidManifest.xml:6:5-43
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\8528862a611804d35d26316bb37d3b92\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\8528862a611804d35d26316bb37d3b92\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c75f16fb3532e702d63c57107fce1c7\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c75f16fb3532e702d63c57107fce1c7\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\83cce0b53247cf5607abf9a7fd5167ef\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\83cce0b53247cf5607abf9a7fd5167ef\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\377b6447c16dbc5d000cfe1af300650d\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\377b6447c16dbc5d000cfe1af300650d\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4b5777ed0d4bffa3f904e5b76ee57f60\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4b5777ed0d4bffa3f904e5b76ee57f60\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\de074a4a358f597debea3416bcaa465b\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\de074a4a358f597debea3416bcaa465b\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a33641c1b8e8c40de42d22eab0154eb5\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a33641c1b8e8c40de42d22eab0154eb5\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b81fee8ac482d588760b3e70ccd1be41\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b81fee8ac482d588760b3e70ccd1be41\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7be3f3db6c752ace90a9d984f67f1bed\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7be3f3db6c752ace90a9d984f67f1bed\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\709da5e1d2fd883e92f87ad1d3d223db\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\709da5e1d2fd883e92f87ad1d3d223db\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7dcaaec5891935906304315c5e8854e0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7dcaaec5891935906304315c5e8854e0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e4ce545fcd52fce8e813db51c5cadbd\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e4ce545fcd52fce8e813db51c5cadbd\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e33cfc4915e44ae2c9dba5d1887dc68\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e33cfc4915e44ae2c9dba5d1887dc68\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d822517d86f4768802c1e5413aee44b7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d822517d86f4768802c1e5413aee44b7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9cce644cafebd40ffd9b00f6518ccfc\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9cce644cafebd40ffd9b00f6518ccfc\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f15a1f0b657363ac1155c0f1898a532\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f15a1f0b657363ac1155c0f1898a532\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\0168da5f54e3519cfdce27f2e9820640\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\0168da5f54e3519cfdce27f2e9820640\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:7:5-9:41
MERGED from [me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\52482d92e32156ad3b52bb767e08bae5\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\52482d92e32156ad3b52bb767e08bae5\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [io.github.jeremyliao:lebx-processor-gson:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\9aed58bccf8b32935a6e7dd7029d817a\transformed\jetified-lebx-processor-gson-1.8.0\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.jeremyliao:lebx-processor-gson:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\9aed58bccf8b32935a6e7dd7029d817a\transformed\jetified-lebx-processor-gson-1.8.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:lp-public-cmp:1.9.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\9805520c9e9935306579be110e607219\transformed\jetified-lp-public-cmp-1.9.3.5\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:lp-public-cmp:1.9.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\9805520c9e9935306579be110e607219\transformed\jetified-lp-public-cmp-1.9.3.5\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:lp-connectsdk:1.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\12b3884315fe7041a0ff571808979902\transformed\jetified-lp-connectsdk-1.0.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.alink.linksdk:lp-connectsdk:1.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\12b3884315fe7041a0ff571808979902\transformed\jetified-lp-connectsdk-1.0.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.alink.linksdk:iot-h2-stream:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\ffb78f1d2813c7eaae96662549550197\transformed\jetified-iot-h2-stream-1.1.6\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:iot-h2-stream:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\ffb78f1d2813c7eaae96662549550197\transformed\jetified-iot-h2-stream-1.1.6\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.alink.linksdk:public-channel-gateway:1.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\89a67f32f2b6dbb5229bb80cd6225108\transformed\jetified-public-channel-gateway-1.6.4\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:public-channel-gateway:1.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\89a67f32f2b6dbb5229bb80cd6225108\transformed\jetified-public-channel-gateway-1.6.4\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.alink.linksdk:tools:1.3.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c9ad81920ad96590b6c7c72106da5b5\transformed\jetified-tools-1.3.5.1\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:tools:1.3.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c9ad81920ad96590b6c7c72106da5b5\transformed\jetified-tools-1.3.5.1\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:iot-h2:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\9ae93d416fb6b56551d4040db1b3ec30\transformed\jetified-iot-h2-1.1.6\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:iot-h2:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\9ae93d416fb6b56551d4040db1b3ec30\transformed\jetified-iot-h2-1.1.6\AndroidManifest.xml:10:5-12:41
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\a6a3a9b2b75d99a97087666cb907f0ff\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\a6a3a9b2b75d99a97087666cb907f0ff\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.aliyun.alink.linksdk:api-client-biz:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b5c364f047faafa411b81befedb2ecf\transformed\jetified-api-client-biz-1.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.alink.linksdk:api-client-biz:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b5c364f047faafa411b81befedb2ecf\transformed\jetified-api-client-biz-1.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.alink.linksdk:android_alcs_lpbs:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e43030a83b65b1cf7711375223358c6\transformed\jetified-android_alcs_lpbs-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:android_alcs_lpbs:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e43030a83b65b1cf7711375223358c6\transformed\jetified-android_alcs_lpbs-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\31345907932cb16ab07cdd18e821ff6d\transformed\jetified-public-alcs-cmp-1.6.0\AndroidManifest.xml:10:5-12:41
MERGED from [com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\31345907932cb16ab07cdd18e821ff6d\transformed\jetified-public-alcs-cmp-1.6.0\AndroidManifest.xml:10:5-12:41
	android:targetSdkVersion
		INJECTED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\mpd_app_dev\app\src\main\AndroidManifest.xml
uses-permission#android.permission.READ_LOGS
ADDED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:17:5-19:47
	tools:ignore
		ADDED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:19:9-44
	android:name
		ADDED from [lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:18:9-52
service#io.getstream.log.android.file.StreamLogFileService
ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:25:9-35:19
	android:enabled
		ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:27:13-35
	android:exported
		ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:26:13-78
intent-filter#action:name:io.getstream.log.android.SHARE
ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:29:13-31:29
action#io.getstream.log.android.SHARE
ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:30:17-73
	android:name
		ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:30:25-70
intent-filter#action:name:io.getstream.log.android.CLEAR
ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:32:13-34:29
action#io.getstream.log.android.CLEAR
ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:33:17-73
	android:name
		ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:33:25-70
provider#io.getstream.log.android.file.StreamLogFileProvider
ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:37:9-45:20
	android:grantUriPermissions
		ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:41:13-47
	android:authorities
		ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:39:13-73
	android:exported
		ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:40:13-37
	android:name
		ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:38:13-79
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:42:13-44:58
	android:resource
		ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:44:17-55
	android:name
		ADDED from [io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:43:17-67
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:10:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-exoplayer:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\efd467293e0b9b5a51777a2be79e83eb\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\efd467293e0b9b5a51777a2be79e83eb\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a53940ce7983efb289ed3d9246f4a3b\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a53940ce7983efb289ed3d9246f4a3b\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:22:5-79
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:12:5-79
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:12:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [com.aliyun.alink.linksdk:lp-connectsdk:1.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\12b3884315fe7041a0ff571808979902\transformed\jetified-lp-connectsdk-1.0.6\AndroidManifest.xml:11:5-79
MERGED from [com.aliyun.alink.linksdk:lp-connectsdk:1.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\12b3884315fe7041a0ff571808979902\transformed\jetified-lp-connectsdk-1.0.6\AndroidManifest.xml:11:5-79
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:15:5-79
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:15:5-79
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:12:5-79
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:12:5-79
MERGED from [com.aliyun.alink.linksdk:iot-h2:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\9ae93d416fb6b56551d4040db1b3ec30\transformed\jetified-iot-h2-1.1.6\AndroidManifest.xml:15:5-79
MERGED from [com.aliyun.alink.linksdk:iot-h2:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\9ae93d416fb6b56551d4040db1b3ec30\transformed\jetified-iot-h2-1.1.6\AndroidManifest.xml:15:5-79
	android:name
		ADDED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:10:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:12:5-76
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:13:5-76
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:13:5-76
MERGED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:14:5-76
MERGED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:14:5-76
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:14:5-76
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:14:5-76
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:13:5-76
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:13:5-76
	android:name
		ADDED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:12:22-73
uses-permission#android.permission.READ_PHONE_STATE
ADDED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:13:5-75
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:15:5-75
MERGED from [com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10] C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-1.7.5.10\AndroidManifest.xml:15:5-75
MERGED from [com.aliyun.alink.linksdk:api-client-biz:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b5c364f047faafa411b81befedb2ecf\transformed\jetified-api-client-biz-1.0.0\AndroidManifest.xml:12:5-75
MERGED from [com.aliyun.alink.linksdk:api-client-biz:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b5c364f047faafa411b81befedb2ecf\transformed\jetified-api-client-biz-1.0.0\AndroidManifest.xml:12:5-75
	android:name
		ADDED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:13:22-72
uses-permission#android.permission.READ_PRIVILEGED_PHONE_STATE
ADDED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:15:5-17:47
	tools:ignore
		ADDED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:17:9-44
	android:name
		ADDED from [com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:16:9-70
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\5dcf23016f5fd81271748c4a43ea45ef\transformed\jetified-camera-core-1.4.2\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\5dcf23016f5fd81271748c4a43ea45ef\transformed\jetified-camera-core-1.4.2\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:31:17-103
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1\AndroidManifest.xml:10:9-12:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1\AndroidManifest.xml:12:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1\AndroidManifest.xml:11:13-84
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\83cce0b53247cf5607abf9a7fd5167ef\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\83cce0b53247cf5607abf9a7fd5167ef\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:16:5-68
MERGED from [com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-0.7.7.5\AndroidManifest.xml:16:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.airdoc.mpd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.airdoc.mpd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#android.permission.MOUNT_UNMOUNT_FILESYSTEMS
ADDED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:29:5-84
	android:name
		ADDED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:29:22-81
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
provider#me.jessyan.autosize.InitProvider
ADDED from [me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:12:9-16:43
	android:authorities
		ADDED from [me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:14:13-74
	android:multiprocess
		ADDED from [me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:16:13-40
	android:exported
		ADDED from [me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:15:13-37
	android:name
		ADDED from [me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:13:13-60
uses-permission#android.permission.CHANGE_WIFI_MULTICAST_STATE
ADDED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:15:5-86
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:13:5-86
MERGED from [com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1] C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\AndroidManifest.xml:13:5-86
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:18:5-86
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:18:5-86
	android:name
		ADDED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:15:22-83
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:16:5-76
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:19:5-76
MERGED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:19:5-76
	android:name
		ADDED from [com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:16:22-73
uses-permission#android.permission.READ_SETTINGS
ADDED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:14:5-72
	android:name
		ADDED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:14:22-69
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:20:5-79
	android:name
		ADDED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:20:22-76
uses-permission#android.permission.OVERRIDE_WIFI_CONFIG
ADDED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:21:5-79
	android:name
		ADDED from [com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:21:22-76
